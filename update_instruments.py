#!/usr/bin/env python3
"""
Update instrument files with correct naming
"""

from utils.instrument_updater import InstrumentUpdater
from utils.logger import logger

def main():
    print("🔄 Updating instrument files...")
    
    # Initialize instrument updater
    updater = InstrumentUpdater()
    
    # Force update to create new file with correct naming
    success = updater.update_instruments(force_update=True)
    
    if success:
        print("✅ Instrument files updated successfully!")
        
        # Show status
        status = updater.get_update_status()
        print(f"📁 Latest file: {status.get('latest_file', 'None')}")
        if status.get('latest_file_size_mb'):
            print(f"📊 File size: {status['latest_file_size_mb']} MB")
        if status.get('latest_file_date'):
            print(f"📅 File date: {status['latest_file_date']}")
    else:
        print("❌ Failed to update instrument files")

if __name__ == "__main__":
    main()
