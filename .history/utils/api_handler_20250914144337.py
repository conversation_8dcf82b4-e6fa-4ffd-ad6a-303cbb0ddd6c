from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
import glob
import os
import csv
from SmartApi import SmartConnect
import pyotp
from trading_config import config
from .logger import logger

@dataclass
class TradeSignal:
    symbol: str
    action: str
    strike: Optional[float] = None
    option_type: Optional[str] = None
    timestamp: datetime = datetime.now()
    quantity: int = 1
    price: Optional[float] = None
    stop_loss: Optional[float] = None
    entry_price: Optional[float] = None
    exit_price: Optional[float] = None
    current_price: Optional[float] = None
    exit_reason: Optional[str] = None
    signal_type: Optional[str] = None  # TRADE, INTIMATION, UPDATE
    expiry_date: Optional[str] = None  # e.g., "18 SEP"
    update_type: Optional[str] = None  # For UPDATE signals like "STOP LOSS"
    new_stop_loss: Optional[float] = None  # For stop loss updates

class APIHandler:
    def __init__(self):
        self.logger = logger.get_logger('api')
        self._ensure_dependencies_dir()  # Ensure Dependencies directory exists
        self.smart_api = self._init_smartapi()
        self.instrument_df = self._load_instruments()
        self.active_trades = {}
        self.pending_stop_loss_updates = {}  # Track pending stop loss updates
        self.trade_log_dir = "trade_logs"
        self._ensure_trade_log_dir()

    def _ensure_dependencies_dir(self):
        """Ensure Dependencies directory exists with proper structure"""
        try:
            dependencies_dir = config.INSTRUMENT_FILE_PATH
            os.makedirs(dependencies_dir, exist_ok=True)

            # Clean up old files on startup (keep last 3 days)
            self._cleanup_old_instrument_files(keep_days=3)

        except Exception as e:
            self.logger.error(f"Error ensuring dependencies directory: {e}")

    def _cleanup_old_instrument_files(self, keep_days: int = 3):
        """Clean up old instrument files similar to the enhanced example"""
        try:
            current_date = datetime.now().strftime("%Y-%m-%d")
            dependencies_dir = config.INSTRUMENT_FILE_PATH

            if not os.path.exists(dependencies_dir):
                return

            files_removed = 0
            for item in os.listdir(dependencies_dir):
                if (item.startswith('smartapi_instruments_') or item.startswith('all_instrument')) and item.endswith('.csv'):
                    # Don't remove today's file
                    if current_date in item:
                        continue

                    file_path = os.path.join(dependencies_dir, item)
                    if os.path.isfile(file_path):
                        try:
                            file_date = datetime.fromtimestamp(os.path.getctime(file_path))
                            cutoff_date = datetime.now() - timedelta(days=keep_days)

                            if file_date < cutoff_date:
                                os.remove(file_path)
                                self.logger.info(f"🗑️ Removed old instrument file: {item}")
                                files_removed += 1
                        except Exception as file_error:
                            self.logger.warning(f"Could not remove {item}: {file_error}")

            if files_removed > 0:
                self.logger.info(f"🧹 Cleanup complete: {files_removed} old files removed")

        except Exception as e:
            self.logger.error(f"Error cleaning up old instrument files: {e}")

    def _init_smartapi(self) -> Optional[SmartConnect]:
        """Initialize SmartAPI client with enhanced error handling and retry logic"""
        max_attempts = config.SMARTAPI_MAX_RECONNECT_ATTEMPTS

        self.logger.info("🔄 Initializing SmartAPI connection...")
        self.logger.info(f"API Key: {config.SMARTAPI_API_KEY[:4]}****")
        self.logger.info(f"User ID: {config.SMARTAPI_USER_ID}")
        self.logger.info(f"Max attempts: {max_attempts}")

        for attempt in range(max_attempts):
            try:
                self.logger.info(f"🔄 SmartAPI connection attempt {attempt + 1}/{max_attempts}")

                # Create SmartConnect object
                obj = SmartConnect(api_key=config.SMARTAPI_API_KEY)
                self.logger.info("✅ SmartConnect object created")

                # Generate TOTP
                totp = pyotp.TOTP(config.SMARTAPI_TOTP_SECRET)
                current_totp = totp.now()
                self.logger.info(f"🔐 Generated TOTP: {current_totp}")

                # Generate session with retry logic
                self.logger.info("🔄 Attempting to generate session...")
                data = obj.generateSession(
                    config.SMARTAPI_USER_ID,
                    config.SMARTAPI_PIN,
                    current_totp
                )

                self.logger.info(f"📡 Session response: {data}")

                if data and data.get('status'):
                    self.logger.info("✅ SmartAPI session generated successfully")
                    logger.log_trade_event('api', f"✅ SmartAPI client initialized successfully (attempt {attempt + 1})")

                    # Test the connection by getting profile
                    try:
                        profile = obj.getProfile(data.get('data', {}).get('refreshToken', ''))
                        if profile and profile.get('status'):
                            self.logger.info(f"✅ Profile verified: {profile.get('data', {}).get('name', 'Unknown')}")
                        else:
                            self.logger.warning("⚠️ Profile verification failed but session is valid")
                    except Exception as profile_error:
                        self.logger.warning(f"⚠️ Profile check failed: {profile_error}")

                    return obj
                else:
                    error_msg = data.get('message', 'Unknown error') if data else 'No response'
                    self.logger.error(f"❌ SmartAPI session failed (attempt {attempt + 1}): {error_msg}")

                    # Log detailed error information
                    if data:
                        self.logger.error(f"Full response: {data}")

                    if attempt < max_attempts - 1:
                        import time
                        self.logger.info(f"⏳ Waiting {config.SMARTAPI_AUTH_DELAY}s before retry...")
                        time.sleep(config.SMARTAPI_AUTH_DELAY)

            except Exception as e:
                self.logger.error(f"❌ SmartAPI initialization error (attempt {attempt + 1}): {e}")
                self.logger.error(f"Exception type: {type(e).__name__}")
                import traceback
                self.logger.error(f"Traceback: {traceback.format_exc()}")

                if attempt < max_attempts - 1:
                    import time
                    self.logger.info(f"⏳ Waiting {config.SMARTAPI_AUTH_DELAY}s before retry...")
                    time.sleep(config.SMARTAPI_AUTH_DELAY)

        self.logger.error(f"❌ SmartAPI initialization failed after {max_attempts} attempts")
        return None

    def test_smartapi_connection(self) -> Dict[str, Any]:
        """Test SmartAPI connection and return status"""
        try:
            if not self.smart_api:
                return {
                    "status": "error",
                    "message": "SmartAPI not initialized",
                    "connected": False
                }

            # Test with a simple API call - get profile
            try:
                # Try to get RMS limits as a test
                response = self.smart_api.rmsLimit()
                if response and response.get('status'):
                    return {
                        "status": "success",
                        "message": "SmartAPI connection is working",
                        "connected": True,
                        "data": response.get('data', {})
                    }
                else:
                    return {
                        "status": "warning",
                        "message": f"SmartAPI responded but with error: {response.get('message', 'Unknown error')}",
                        "connected": False,
                        "response": response
                    }
            except Exception as test_error:
                return {
                    "status": "error",
                    "message": f"SmartAPI connection test failed: {test_error}",
                    "connected": False
                }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Connection test error: {e}",
                "connected": False
            }

    def _get_latest_instrument_file(self) -> Optional[str]:
        """Get the latest instrument file from the Dependencies directory"""
        try:
            # Look for both new and legacy naming patterns
            patterns = [
                os.path.join(config.INSTRUMENT_FILE_PATH, "smartapi_instruments_*.csv"),
                os.path.join(config.INSTRUMENT_FILE_PATH, "all_instrument*.csv")  # Legacy support
            ]

            files = []
            for pattern in patterns:
                files.extend(glob.glob(pattern))

            if not files:
                self.logger.error("No instrument files found")
                return None

            latest_file = max(files, key=os.path.getctime)
            self.logger.info(f"Using instrument file: {os.path.basename(latest_file)}")
            return latest_file
        except Exception as e:
            self.logger.error(f"Error finding instrument file: {e}")
            return None
            
    def _load_instruments(self) -> pd.DataFrame:
        """Enhanced instrument loading with automatic download and caching"""
        try:
            # Try to get existing file first
            filepath = self._get_latest_instrument_file()

            if filepath and os.path.exists(filepath):
                # Check if file is from today
                file_date = datetime.fromtimestamp(os.path.getctime(filepath)).strftime("%Y-%m-%d")
                today = datetime.now().strftime("%Y-%m-%d")

                if file_date == today:
                    # Read existing file
                    df = pd.read_csv(filepath, low_memory=False)
                    logger.log_trade_event('api', f"✅ Loaded {len(df)} instruments from {os.path.basename(filepath)}")
                    return df
                else:
                    self.logger.info(f"Instrument file is outdated ({file_date}), will download fresh data")

            # No valid file found, download fresh data
            self.logger.info("📥 Downloading fresh instrument data...")
            from .instrument_updater import InstrumentUpdater

            updater = InstrumentUpdater()
            success = updater.update_instruments(force_update=True)

            if success:
                # Try to load the newly downloaded file
                filepath = self._get_latest_instrument_file()
                if filepath and os.path.exists(filepath):
                    df = pd.read_csv(filepath, low_memory=False)
                    logger.log_trade_event('api', f"✅ Loaded {len(df)} fresh instruments from {os.path.basename(filepath)}")
                    return df

            self.logger.error("Failed to download or load instrument data")
            return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"Failed to load instruments: {e}")
            return pd.DataFrame()

    def find_instrument(self, signal: TradeSignal) -> Optional[Dict]:
        """Find instrument details from the loaded instrument data"""
        try:
            if signal.strike and signal.option_type and signal.expiry_date:
                # Option instrument - match using the actual data format
                # Convert expiry "16 SEP" to "16SEP2025" format
                expiry_parts = signal.expiry_date.split()
                if len(expiry_parts) == 2:
                    day, month = expiry_parts
                    # Use current year for options (most common case)
                    from datetime import datetime
                    current_year = datetime.now().year
                    year = str(current_year)
                    formatted_expiry = f"{day}{month}{year}"
                else:
                    formatted_expiry = signal.expiry_date

                # Convert strike to paise (multiply by 100)
                strike_in_paise = signal.strike * 100

                # Build expected trading symbol format
                expected_trading_symbol = f"{signal.symbol}{formatted_expiry}{int(signal.strike * 100)}{signal.option_type}"

                self.logger.info(f"Looking for instrument with trading symbol: {expected_trading_symbol}")

                # First try exact match on trading symbol
                mask = self.instrument_df['SEM_TRADING_SYMBOL'] == expected_trading_symbol
                matches = self.instrument_df[mask]

                if matches.empty:
                    # If no exact match, try component-wise matching
                    mask = (
                        (self.instrument_df['SEM_CUSTOM_SYMBOL'] == signal.symbol) &
                        (self.instrument_df['SEM_STRIKE_PRICE'] == strike_in_paise) &
                        (self.instrument_df['SEM_EXPIRY_DATE'].str.contains(formatted_expiry, na=False))
                    )
                    matches = self.instrument_df[mask]

                    # If still no match, try with option type conversion
                    if matches.empty:
                        option_type_in_symbol = "CE" if signal.option_type == "CE" else "PE"
                        mask = (
                            (self.instrument_df['SEM_CUSTOM_SYMBOL'] == signal.symbol) &
                            (self.instrument_df['SEM_STRIKE_PRICE'] == strike_in_paise) &
                            (self.instrument_df['SEM_TRADING_SYMBOL'].str.contains(option_type_in_symbol, na=False))
                        )
                        matches = self.instrument_df[mask]

            elif signal.strike and signal.option_type:
                # Option instrument without expiry date - match by symbol, strike, and option type
                mask = (
                    (self.instrument_df['SEM_CUSTOM_SYMBOL'].str.contains(signal.symbol, na=False)) &
                    (self.instrument_df['SEM_STRIKE_PRICE'] == signal.strike) &
                    (self.instrument_df['SEM_OPTION_TYPE'] == signal.option_type)
                )
                matches = self.instrument_df[mask]
            else:
                # Stock/Index instrument - match by symbol name
                mask = self.instrument_df['SEM_CUSTOM_SYMBOL'].str.contains(f"^{signal.symbol}$", na=False, regex=True)
                matches = self.instrument_df[mask]

            if not matches.empty:
                # Convert to the expected format for API calls
                instrument = matches.iloc[0]
                return {
                    'exchange': instrument['SEM_EXM_EXCH_ID'],
                    'tradingsymbol': instrument['SEM_TRADING_SYMBOL'],
                    'token': str(instrument['SEM_SMST_SECURITY_ID']),
                    'symbol': signal.symbol,
                    'strike': instrument.get('SEM_STRIKE_PRICE', 0),
                    'option_type': instrument.get('SEM_OPTION_TYPE', ''),
                    'custom_symbol': instrument['SEM_CUSTOM_SYMBOL'],
                    'lot_size': int(instrument.get('SEM_LOT_SIZE', 1))  # Add lot size
                }
            else:
                self.logger.warning(f"No instrument found for signal: {signal}")
                return None

        except Exception as e:
            self.logger.error(f"Error finding instrument: {e}")
            return None

    def get_market_data(self, instrument: Dict, retry_count: int = 3) -> Optional[Dict]:
        """Get current market data for an instrument with robust error handling and retry logic"""
        for attempt in range(retry_count):
            try:
                if not self.smart_api:
                    self.logger.warning("SmartAPI not initialized")
                    return None

                self.logger.debug(f"Fetching market data for {instrument['tradingsymbol']} (attempt {attempt + 1})")

                # Use ltpData method from SmartAPI
                data = self.smart_api.ltpData(
                    exchange=instrument['exchange'],
                    tradingsymbol=instrument['tradingsymbol'],
                    symboltoken=instrument['token']
                )

                self.logger.debug(f"Market data response: {data}")

                # Handle different response formats
                if data and isinstance(data, dict) and data.get('status'):
                    market_data = data.get('data', {})
                    if market_data:
                        self.logger.debug(f"✅ Market data fetched: LTP={market_data.get('ltp', 'N/A')}")
                        return market_data
                    else:
                        self.logger.warning(f"Empty market data received (attempt {attempt + 1})")
                elif data and isinstance(data, dict):
                    error_msg = data.get('message', 'Unknown error')
                    self.logger.warning(f"API returned error (attempt {attempt + 1}): {error_msg}")

                    # Check if it's a rate limit error and wait
                    if 'rate limit' in error_msg.lower() or 'too many requests' in error_msg.lower():
                        if attempt < retry_count - 1:
                            import time
                            self.logger.info(f"Rate limit hit, waiting {config.SMARTAPI_RATE_LIMIT_DELAY}s...")
                            time.sleep(config.SMARTAPI_RATE_LIMIT_DELAY)
                            continue

                    # Check for authentication errors
                    if 'invalid' in error_msg.lower() or 'unauthorized' in error_msg.lower():
                        self.logger.error("Authentication error - attempting to reinitialize SmartAPI")
                        self.smart_api = self._init_smartapi()
                        if not self.smart_api:
                            return None

                    return None
                else:
                    self.logger.warning(f"Invalid response format from market data API (attempt {attempt + 1}): {type(data)}")
                    if attempt < retry_count - 1:
                        import time
                        time.sleep(1)
                        continue
                    return None

            except Exception as e:
                self.logger.error(f"Error fetching market data (attempt {attempt + 1}): {e}")
                self.logger.error(f"Exception type: {type(e).__name__}")

                # Check if it's a connection error
                if 'connection' in str(e).lower() or 'timeout' in str(e).lower():
                    self.logger.warning("Connection issue detected, attempting to reinitialize SmartAPI")
                    self.smart_api = self._init_smartapi()

                if attempt < retry_count - 1:
                    import time
                    time.sleep(2)
                    continue
                return None

        self.logger.error(f"Failed to fetch market data after {retry_count} attempts")
        return None

    def process_signal(self, signal: TradeSignal) -> Dict[str, Any]:
        """Process incoming trade signal with comprehensive order management and fallback handling"""
        try:
            # Always log the signal for audit purposes
            self._log_signal_to_csv(signal)

            # Initialize SmartAPI if market data fetching is enabled
            if config.ENABLE_MARKET_DATA_FETCHING and not self.smart_api:
                self.logger.warning("SmartAPI not available, attempting to reinitialize...")
                self.smart_api = self._init_smartapi()

            # Find instrument details (needed for both trading and market data)
            instrument = self.find_instrument(signal)
            if not instrument:
                return {"status": "error", "message": f"Instrument not found for {signal.symbol}"}

            # Handle different signal types and actions
            if signal.signal_type == "UPDATE" and signal.action == "UPDATE":
                return self._handle_stop_loss_update(signal, instrument)
            elif signal.action.upper() in ['BUY', 'SELL']:
                return self._handle_entry_order(signal, instrument)
            elif signal.action.upper() in ['CLOSE', 'EXIT']:
                return self._handle_exit_order(signal, instrument)
            elif signal.action.upper() == 'HOLD':
                return self._handle_hold_signal(signal, instrument)
            else:
                return {"status": "error", "message": f"Invalid action {signal.action}"}

        except Exception as e:
            self.logger.error(f"Signal processing failed: {e}")
            return {"status": "error", "message": f"Signal processing error: {str(e)}"}

    def _execute_trade(self, signal: TradeSignal, instrument: Dict, market_data: Dict) -> Dict[str, Any]:
        """Execute trade through SmartAPI with proper lot size handling"""
        try:
            # Calculate proper quantity based on lot size
            lot_size = instrument.get('lot_size', 1)

            # Determine the number of lots to trade
            if signal.quantity > 0:
                # Use signal-specified quantity
                num_lots = signal.quantity
            else:
                # Use custom lot size if configured, otherwise use default
                num_lots = config.get_lot_size_for_option(
                    symbol=signal.symbol,
                    strike=signal.strike,
                    option_type=signal.option_type,
                    expiry_date=signal.expiry_date
                )

            actual_quantity = num_lots * lot_size

            self.logger.info(f"Executing trade: {signal.action} {actual_quantity} units ({num_lots} lots x {lot_size}) of {instrument['tradingsymbol']}")

            # Log lot size determination for debugging
            if signal.strike and signal.option_type:
                option_key = config.create_option_chain_key(signal.symbol, signal.strike, signal.option_type, signal.expiry_date or "")
                if option_key in config.CUSTOM_LOT_SIZES:
                    self.logger.info(f"Using custom lot size {num_lots} for option chain: {option_key}")
                else:
                    self.logger.info(f"Using default lot size {num_lots} for {signal.symbol}")

            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": instrument['tradingsymbol'],
                "symboltoken": instrument['token'],
                "transactiontype": signal.action.upper(),
                "exchange": instrument['exchange'],
                "ordertype": "MARKET",
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": "0",
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(actual_quantity)  # Use actual quantity (lots * lot_size)
            }

            # Place order with error handling
            response = None
            try:
                response = self.smart_api.placeOrder(order_params)
            except Exception as order_error:
                self.logger.error(f"Order placement API call failed: {order_error}")
                return {"status": "error", "message": f"Order API error: {str(order_error)}"}

            # Handle different response formats from SmartAPI
            success = False
            order_id = None

            if response:
                if isinstance(response, dict) and response.get('status'):
                    # Standard JSON response format
                    order_id = response.get('data', {}).get('orderid')
                    success = True
                elif isinstance(response, str) and len(response) > 5:
                    # Sometimes SmartAPI returns just the order ID as a string
                    order_id = response
                    success = True
                    logger.log_trade_event('api', f"Order placed successfully with ID: {order_id}")
                elif isinstance(response, dict) and not response.get('status'):
                    # Error response
                    error_msg = response.get('message', 'Order placement failed')
                    self.logger.error(f"Order rejected by broker: {error_msg}")
                    return {"status": "error", "message": f"Broker rejected order: {error_msg}"}
                else:
                    success = False
                    order_id = None
            else:
                success = False
                order_id = None

            if success and order_id:
                # Safely get LTP from market data
                ltp_price = None
                if market_data and isinstance(market_data, dict):
                    ltp_price = market_data.get('ltp')

                self.active_trades[signal.symbol] = {
                    "order_id": order_id,
                    "entry_time": datetime.now(),
                    "entry_price": ltp_price or signal.price,
                    "quantity": actual_quantity,
                    "lots": num_lots,
                    "lot_size": lot_size,
                    "action": signal.action,
                    "instrument": instrument,
                    "stop_loss": signal.stop_loss,
                    "last_update": datetime.now(),
                    "current_price": ltp_price or signal.price
                }

                # Log successful trade
                logger.log_trade_event('api', f"ORDER PLACED: {signal.action} {actual_quantity} units of {instrument['tradingsymbol']} at {ltp_price or signal.price}")

                # Log to CSV
                trade_data = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': signal.symbol,
                    'action': signal.action,
                    'quantity': actual_quantity,
                    'lots': num_lots,
                    'price': ltp_price or signal.price,
                    'order_id': order_id,
                    'stop_loss': signal.stop_loss,
                    'status': 'PLACED',
                    'reason': 'entry_signal'
                }
                self._log_trade_to_csv(trade_data)

                return {
                    "status": "success",
                    "message": f"Order placed successfully: {signal.action} {actual_quantity} units",
                    "order_id": order_id,
                    "price": ltp_price or signal.price,
                    "quantity": actual_quantity,
                    "lots": num_lots
                }
            else:
                # Handle error response safely
                if response and isinstance(response, dict):
                    error_msg = response.get('message', 'Order placement failed')
                elif response and isinstance(response, str):
                    error_msg = f"Unexpected string response: {response}"
                elif response:
                    error_msg = f"Unexpected response format: {str(response)}"
                else:
                    error_msg = 'No response from API'

                self.logger.error(f"Order placement failed: {error_msg}")
                return {"status": "error", "message": error_msg}

        except Exception as e:
            self.logger.error(f"Trade execution failed: {e}")
            return {"status": "error", "message": f"Trade execution error: {str(e)}"}

    def _exit_position(self, symbol: str) -> Dict[str, Any]:
        """Exit an existing position"""
        try:
            if symbol not in self.active_trades:
                return {"status": "error", "message": f"No active position found for {symbol}"}
                
            position = self.active_trades[symbol]
            exit_action = "SELL" if position['action'] == "BUY" else "BUY"
            instrument = position['instrument']
            
            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": instrument['tradingsymbol'],
                "symboltoken": instrument['token'],
                "transactiontype": exit_action,
                "exchange": instrument['exchange'],
                "ordertype": "MARKET",
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": "0",
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(position['quantity'])
            }
            
            response = self.smart_api.placeOrder(order_params)

            # Handle different response formats from SmartAPI (same as in _execute_trade)
            if response:
                if isinstance(response, dict) and response.get('status'):
                    # Standard JSON response format
                    order_id = response.get('data', {}).get('orderid')
                    success = True
                elif isinstance(response, str) and len(response) > 5:
                    # Sometimes SmartAPI returns just the order ID as a string
                    order_id = response
                    success = True
                    self.logger.info(f"Exit order placed successfully with ID: {order_id}")
                else:
                    success = False
                    order_id = None
            else:
                success = False
                order_id = None

            if success and order_id:
                # Log to CSV
                trade_data = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': symbol,
                    'action': exit_action,
                    'quantity': position['quantity'],
                    'lots': position['lots'],
                    'price': 'MARKET',
                    'order_id': order_id,
                    'stop_loss': position.get('stop_loss'),
                    'status': 'CLOSED',
                    'reason': 'exit_signal'
                }
                self._log_trade_to_csv(trade_data)

                del self.active_trades[symbol]
                return {
                    "status": "success",
                    "message": f"Position exited successfully: {exit_action} {position['quantity']} units",
                    "order_id": order_id
                }
            else:
                # Handle error response safely
                if response and isinstance(response, dict):
                    error_msg = response.get('message', 'Exit order failed')
                elif response and isinstance(response, str):
                    error_msg = f"Unexpected string response: {response}"
                elif response:
                    error_msg = f"Unexpected response format: {str(response)}"
                else:
                    error_msg = 'No response from API'

                return {"status": "error", "message": error_msg}
                
        except Exception as e:
            self.logger.error(f"Position exit failed: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_entry_order(self, signal: TradeSignal, instrument: Dict) -> Dict[str, Any]:
        """Handle BUY/SELL entry orders with simulation mode support"""
        try:
            # Get market data if enabled (for both simulation and live trading)
            market_data = None
            if config.ENABLE_MARKET_DATA_FETCHING:
                market_data = self.get_market_data(instrument)
                if not market_data:
                    logger.log_trade_event('api', f"⚠️ Unable to fetch market data for {signal.symbol}")

            # Check if actual trading is enabled
            if not config.ENABLE_ACTUAL_TRADING:
                # Simulation mode - log the trade but don't execute
                current_price = None
                if market_data:
                    current_price = market_data.get('ltp', signal.price)

                # Determine lot size using the new configurable system
                num_lots = config.get_lot_size_for_option(
                    symbol=signal.symbol,
                    strike=signal.strike,
                    option_type=signal.option_type,
                    expiry_date=signal.expiry_date
                )

                logger.log_trade_event('api', f"SIMULATION: {signal.action} {signal.symbol} at {current_price or signal.price} (Entry: {signal.price}) - {num_lots} lots")

                # Log to CSV with simulation flag
                trade_data = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': signal.symbol,
                    'action': signal.action,
                    'quantity': num_lots * instrument.get('lot_size', 1),
                    'lots': num_lots,
                    'price': current_price or signal.price,
                    'order_id': 'SIMULATION',
                    'stop_loss': signal.stop_loss,
                    'status': 'SIMULATED',
                    'reason': 'simulation_mode'
                }
                self._log_trade_to_csv(trade_data)

                return {
                    "status": "success",
                    "message": f"SIMULATION: {signal.action} order logged for {signal.symbol} at {current_price or signal.price}"
                }

            # Live trading mode - check market hours
            if not config.is_market_open():
                return {
                    "status": "info",
                    "message": f"Market is closed. Signal logged but trade not executed for {signal.symbol}"
                }

            if not market_data:
                return {
                    "status": "warning",
                    "message": f"Unable to fetch live market data for {signal.symbol}. Trade not executed."
                }

            return self._execute_trade(signal, instrument, market_data)

        except Exception as e:
            self.logger.error(f"Entry order handling failed: {e}")
            return {"status": "error", "message": f"Entry order error: {str(e)}"}

    def _handle_exit_order(self, signal: TradeSignal, instrument: Dict) -> Dict[str, Any]:
        """Handle CLOSE/EXIT orders with simulation mode support"""
        try:
            # Get market data if enabled
            market_data = None
            if config.ENABLE_MARKET_DATA_FETCHING:
                market_data = self.get_market_data(instrument)

            # Check if actual trading is enabled
            if not config.ENABLE_ACTUAL_TRADING:
                # Simulation mode - log the exit but don't execute
                current_price = None
                if market_data:
                    current_price = market_data.get('ltp', signal.exit_price or signal.price)

                # Determine lot size using the new configurable system
                num_lots = config.get_lot_size_for_option(
                    symbol=signal.symbol,
                    strike=signal.strike,
                    option_type=signal.option_type,
                    expiry_date=signal.expiry_date
                )

                logger.log_trade_event('api', f"SIMULATION: CLOSE {signal.symbol} at {current_price or signal.exit_price or signal.price} - {num_lots} lots")

                # Log to CSV with simulation flag
                trade_data = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': signal.symbol,
                    'action': 'CLOSE',
                    'quantity': num_lots * instrument.get('lot_size', 1),
                    'lots': num_lots,
                    'price': current_price or signal.exit_price or signal.price,
                    'order_id': 'SIMULATION',
                    'stop_loss': signal.stop_loss,
                    'status': 'SIMULATED_CLOSE',
                    'reason': signal.exit_reason or 'simulation_exit'
                }
                self._log_trade_to_csv(trade_data)

                return {
                    "status": "success",
                    "message": f"SIMULATION: Close order logged for {signal.symbol} at {current_price or signal.exit_price or signal.price}"
                }

            # Live trading mode - check market hours and execute
            if config.is_market_open():
                return self._exit_position(signal.symbol)
            else:
                # Market is closed, just log the close signal
                return {
                    "status": "info",
                    "message": f"Market is closed. Close signal logged for {signal.symbol} but no order executed."
                }

        except Exception as e:
            self.logger.error(f"Exit order handling failed: {e}")
            return {"status": "error", "message": f"Exit order error: {str(e)}"}

    def _handle_stop_loss_update(self, signal: TradeSignal, instrument: Dict) -> Dict[str, Any]:
        """Handle stop loss update signals (Format4)"""
        try:
            symbol_key = signal.symbol

            # Check if we have an active position for this symbol
            if symbol_key not in self.active_trades:
                # Store pending update for when position is created
                self.pending_stop_loss_updates[symbol_key] = signal.new_stop_loss
                return {
                    "status": "info",
                    "message": f"No active position found for {signal.symbol}. Stop loss update stored for future use."
                }

            # Update stop loss for active position
            self.active_trades[symbol_key]['stop_loss'] = signal.new_stop_loss

            # In a real implementation, you would modify the existing order or place a new stop loss order
            # For now, we'll just log the update
            self.logger.info(f"Updated stop loss for {signal.symbol} to {signal.new_stop_loss}")

            return {
                "status": "success",
                "message": f"Stop loss updated for {signal.symbol} to {signal.new_stop_loss}"
            }

        except Exception as e:
            self.logger.error(f"Stop loss update failed: {e}")
            return {"status": "error", "message": f"Stop loss update error: {str(e)}"}

    def _handle_hold_signal(self, signal: TradeSignal, instrument: Dict) -> Dict[str, Any]:
        """Handle HOLD/INTIMATION signals (Format2)"""
        try:
            symbol_key = signal.symbol

            # Update current price if we have an active position
            if symbol_key in self.active_trades:
                self.active_trades[symbol_key]['current_price'] = signal.current_price
                self.active_trades[symbol_key]['last_update'] = datetime.now()

                # Apply any pending stop loss updates
                if symbol_key in self.pending_stop_loss_updates:
                    self.active_trades[symbol_key]['stop_loss'] = self.pending_stop_loss_updates[symbol_key]
                    del self.pending_stop_loss_updates[symbol_key]
                    self.logger.info(f"Applied pending stop loss update for {signal.symbol}")

            return {
                "status": "success",
                "message": f"Hold signal processed for {signal.symbol} at price {signal.current_price}"
            }

        except Exception as e:
            self.logger.error(f"Hold signal handling failed: {e}")
            return {"status": "error", "message": f"Hold signal error: {str(e)}"}

    def check_and_auto_close_positions(self) -> List[Dict[str, Any]]:
        """Check for positions that haven't received signals within timeout period and auto-close them"""
        try:
            results = []
            current_time = datetime.now()
            timeout_seconds = config.SIGNAL_TIMEOUT_SECONDS

            positions_to_close = []

            for symbol, position in self.active_trades.items():
                last_update = position.get('last_update', position['entry_time'])
                time_since_update = (current_time - last_update).total_seconds()

                if time_since_update > timeout_seconds:
                    positions_to_close.append(symbol)
                    self.logger.warning(f"Position {symbol} hasn't received signal for {time_since_update:.1f}s, auto-closing")

            # Close timed-out positions
            for symbol in positions_to_close:
                result = self._exit_position(symbol)
                result['reason'] = 'auto_close_timeout'
                results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"Auto-close check failed: {e}")
            return [{"status": "error", "message": f"Auto-close error: {str(e)}"}]

    def get_active_positions_summary(self) -> Dict[str, Any]:
        """Get summary of all active positions"""
        try:
            summary = {
                "total_positions": len(self.active_trades),
                "positions": []
            }

            for symbol, position in self.active_trades.items():
                position_info = {
                    "symbol": symbol,
                    "action": position['action'],
                    "entry_price": position['entry_price'],
                    "current_price": position.get('current_price'),
                    "stop_loss": position.get('stop_loss'),
                    "quantity": position['quantity'],
                    "lots": position['lots'],
                    "entry_time": position['entry_time'].isoformat(),
                    "last_update": position.get('last_update', position['entry_time']).isoformat()
                }
                summary["positions"].append(position_info)

            return {"status": "success", "data": summary}

        except Exception as e:
            self.logger.error(f"Position summary failed: {e}")
            return {"status": "error", "message": f"Position summary error: {str(e)}"}

    def force_close_all_positions(self) -> List[Dict[str, Any]]:
        """Force close all active positions (emergency function)"""
        try:
            results = []
            symbols_to_close = list(self.active_trades.keys())

            for symbol in symbols_to_close:
                result = self._exit_position(symbol)
                result['reason'] = 'force_close_all'
                results.append(result)

            self.logger.info(f"Force closed {len(symbols_to_close)} positions")
            return results

        except Exception as e:
            self.logger.error(f"Force close all failed: {e}")
            return [{"status": "error", "message": f"Force close error: {str(e)}"}]

    def _ensure_trade_log_dir(self):
        """Ensure trade log directory exists"""
        if not os.path.exists(self.trade_log_dir):
            os.makedirs(self.trade_log_dir)

    def _get_daily_csv_path(self) -> str:
        """Get path for today's trade log CSV file"""
        today = datetime.now().strftime("%Y-%m-%d")
        return os.path.join(self.trade_log_dir, f"trades_{today}.csv")

    def _log_trade_to_csv(self, trade_data: Dict[str, Any]):
        """Log trade details to daily CSV file"""
        try:
            csv_path = self._get_daily_csv_path()
            file_exists = os.path.exists(csv_path)

            # Define CSV headers
            headers = [
                'timestamp', 'symbol', 'action', 'quantity', 'lots', 'price',
                'order_id', 'stop_loss', 'status', 'reason'
            ]

            with open(csv_path, 'a', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=headers)

                # Write header if file is new
                if not file_exists:
                    writer.writeheader()

                writer.writerow(trade_data)

            logger.log_trade_event('api', f"Trade logged to CSV: {trade_data['action']} {trade_data['symbol']}")

        except Exception as e:
            self.logger.error(f"Failed to log trade to CSV: {e}")

    def _log_signal_to_csv(self, signal: TradeSignal):
        """Log all incoming signals to CSV for audit trail"""
        try:
            csv_path = self._get_daily_csv_path().replace('trades_', 'signals_')
            file_exists = os.path.exists(csv_path)

            # Define CSV headers for signals
            headers = [
                'timestamp', 'signal_type', 'symbol', 'action', 'price',
                'stop_loss', 'entry_price', 'exit_price', 'current_price', 'raw_signal'
            ]

            signal_data = {
                'timestamp': datetime.now().isoformat(),
                'signal_type': signal.signal_type or 'UNKNOWN',
                'symbol': signal.symbol,
                'action': signal.action,
                'price': signal.price,
                'stop_loss': signal.stop_loss,
                'entry_price': signal.entry_price,
                'exit_price': signal.exit_price,
                'current_price': signal.current_price,
                'raw_signal': str(signal)
            }

            with open(csv_path, 'a', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=headers)

                # Write header if file is new
                if not file_exists:
                    writer.writeheader()

                writer.writerow(signal_data)

        except Exception as e:
            self.logger.error(f"Failed to log signal to CSV: {e}")
