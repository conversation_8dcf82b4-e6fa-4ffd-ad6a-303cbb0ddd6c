# Indian Market Trading Bot

An automated trading system for the Indian stock market that processes Telegram signals and executes trades through SmartAPI. Supports both simulation and live trading modes with comprehensive risk management.

## 🎯 Quick Start

### 1. Installation
```bash
git clone <repository-url>
cd tele_client
pip install -r requirements.txt
```

### 2. Configuration
Edit `trading_config.py` with your credentials:
```python
# Telegram Configuration
API_ID = "your_telegram_api_id"
API_HASH = "your_telegram_api_hash"
GROUP_NAME = "your_telegram_group_name"

# SmartAPI Configuration
SMARTAPI_API_KEY = "your_smartapi_key"
SMARTAPI_USER_ID = "your_user_id"
SMARTAPI_PIN = "your_pin"
SMARTAPI_TOTP_SECRET = "your_totp_secret"

# Trading Settings
DEFAULT_LOT_SIZE = 1
ENABLE_ACTUAL_TRADING = False  # Keep False for testing
```

### 3. Start Trading
```bash
# Test mode (recommended first)
python main.py

# Live trading (after testing)
python config_manager.py live
python main.py
```

## 📊 Signal Formats

The bot processes 5 signal formats from Telegram:

1. **BUY Signal**: `===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CE$32.4$30.5$2025-09-16 10:15:00+05:30`
2. **HOLD Signal**: `===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 CE...`
3. **CLOSE Signal**: `===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CE$35.2$2025-09-16 14:30:00+05:30$profit_exit`
4. **STOP LOSS Update**: `===Algo_Trading===$Update$STOP LOSS to$32.0$for option$NIFTY 16 SEP 25000 CE...`
5. **CROSSOVER Exit**: `===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CE$28.5$2025-09-16 15:00:00+05:30$opposite_crossover_exit`

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd tele_client
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the system**:
   Edit `trading_config.py` with your credentials:
   ```python
   # Telegram Configuration
   API_ID = "your_telegram_api_id"
   API_HASH = "your_telegram_api_hash"
   GROUP_NAME = "your_telegram_group_name"
   
   # SmartAPI Configuration
   SMARTAPI_API_KEY = "your_smartapi_key"
   SMARTAPI_USER_ID = "your_user_id"
   SMARTAPI_PIN = "your_pin"
   SMARTAPI_TOTP_SECRET = "your_totp_secret"
   
   # Trading Parameters
   DEFAULT_LOT_SIZE = 1
   ENABLE_ACTUAL_TRADING = False  # Set to True for live trading
   ```

## 🚦 Usage

### Starting the Bot

**Simulation Mode** (recommended for testing):
```bash
python main.py
```

**Live Trading Mode**:
```bash
# Enable live trading (with safety warnings)
python config_manager.py live

# Start the bot
python main.py
```

### Configuration Management

Use the new `config_manager.py` tool for easy configuration:

```bash
# Show current configuration
python config_manager.py show

# Enable simulation mode (safe testing)
python config_manager.py sim

# Enable live trading (with warnings)
python config_manager.py live

# Update lot size
python config_manager.py lot-size --size 2
```

### Configuration Options

Key parameters in `trading_config.py`:

| Parameter | Description | Default |
|-----------|-------------|---------|
| `DEFAULT_LOT_SIZE` | Number of lots per trade | 1 |
| `SIGNAL_TIMEOUT_SECONDS` | Auto-close timeout | 30 |
| `ENABLE_ACTUAL_TRADING` | Enable live trading | False |
| `MAX_POSITIONS` | Maximum concurrent positions | 5 |
| `ENABLE_AUTO_CLOSE` | Auto-close on timeout | True |

### Environment Variables

For production deployment, use environment variables:
```bash
export TELEGRAM_API_ID="your_api_id"
export SMARTAPI_API_KEY="your_api_key"
export ENABLE_ACTUAL_TRADING="true"
export DEFAULT_LOT_SIZE="2"
```

## 📊 Monitoring & Logs

### Log Files
- **Daily Logs**: `logs/trading_YYYY-MM-DD.log`
- **Trade CSV**: `trade_logs/trades_YYYY-MM-DD.csv`
- **Signal CSV**: `trade_logs/signals_YYYY-MM-DD.csv`

### System Status
The bot logs system status every 5 minutes:
```
💓 System Status - Market: OPEN, Positions: 2
```

### Trade Confirmations
All trades are logged with details:
```
TRADE: ORDER PLACED: BUY 75 units of NIFTY16SEP202525000PE at 32.4
```

## 🔧 Advanced Features

### Position Management
- **Active Positions**: View all open positions
- **Auto-Close**: Positions closed if no signal for 30 seconds
- **Emergency Close**: Force close all positions on shutdown

### Error Handling
- **API Retry Logic**: Automatic retry on API failures
- **Fallback Mechanisms**: Graceful degradation when services unavailable
- **Rate Limit Handling**: Automatic delays on rate limit errors

### Risk Controls
- **Market Hours**: Only trades during market hours
- **Position Limits**: Configurable maximum positions
- **Stop Loss**: Automatic stop loss management

## 🚨 Important Notes

1. **Test First**: Always test in simulation mode before live trading
2. **Monitor Logs**: Keep an eye on log files for any issues
3. **Market Hours**: System only trades during Indian market hours
4. **Backup**: Keep backups of your configuration and logs
5. **Updates**: Regularly update instrument files for accurate trading

## 🔒 Security

- Never commit credentials to version control
- Use environment variables for production
- Regularly rotate API keys and secrets
- Monitor trade logs for unauthorized activity

## 📞 Support

For issues or questions:
1. Check the log files for error messages
2. Verify your configuration settings
3. Ensure all dependencies are installed
4. Test with simulation mode first

## 📄 License

This project is for educational and personal use only. Use at your own risk.
