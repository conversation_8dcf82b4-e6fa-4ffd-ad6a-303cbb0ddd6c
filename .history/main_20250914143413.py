#!/usr/bin/env python3
"""
Enhanced Algo Trading Bot
Comprehensive trading system with order management, risk controls, and monitoring
Includes configuration management functionality
"""

import asyncio
import sys
import signal
import argparse
import re
from datetime import datetime, timedelta
from telethon import TelegramClient, events
from trading_config import config
from utils.logger import logger
from utils.signal_handler import SignalHandler

class TradingBot:
    """Main trading bot class with enhanced functionality"""
    
    def __init__(self):
        self.logger = logger.get_logger('main')
        self.signal_handler = SignalHandler()
        self.client = None
        self.running = False
        self.last_heartbeat = datetime.now()
        
    async def initialize(self):
        """Initialize the trading bot"""
        try:
            # Setup logging
            logger.setup(log_dir=config.LOG_DIR)
            
            # Validate configuration
            if not config.validate():
                self.logger.error("Invalid configuration. Please check your settings.")
                return False
                
            # Initialize Telegram client
            self.client = TelegramClient(config.SESSION_NAME, config.API_ID, config.API_HASH)
            
            logger.log_trade_event('main', "🚀 Enhanced Algo Trading Bot Starting...")
            logger.log_trade_event('main', f"Trading Mode: {'LIVE' if config.ENABLE_ACTUAL_TRADING else 'SIMULATION'}")
            logger.log_trade_event('main', f"Default Lot Size: {config.DEFAULT_LOT_SIZE}")
            logger.log_trade_event('main', f"Signal Timeout: {config.SIGNAL_TIMEOUT_SECONDS}s")

            # Also print to console for better visibility
            print("📊 Bot Configuration:")
            print(f"  • Trading Mode: {'🔴 LIVE' if config.ENABLE_ACTUAL_TRADING else '🟡 SIMULATION'}")
            print(f"  • Market Data: {'✅ ENABLED' if config.ENABLE_MARKET_DATA_FETCHING else '❌ DISABLED'}")
            print(f"  • Default Lot Size: {config.DEFAULT_LOT_SIZE}")
            print(f"  • Signal Timeout: {config.SIGNAL_TIMEOUT_SECONDS}s")
            
            await self.client.start()
            
            # Get bot info
            me = await self.client.get_me()
            logger.log_trade_event('main', f"✅ Bot authenticated as {me.username}")
            print(f"✅ Telegram authenticated as: {me.username}")

            return True
            
        except Exception as e:
            self.logger.error(f"Initialization failed: {e}")
            return False
    
    async def find_target_group(self):
        """Find and validate the target Telegram group"""
        try:
            logger.log_trade_event('main', f"🔍 Searching for group: {config.GROUP_NAME}")
            
            async for dialog in self.client.iter_dialogs():
                if dialog.name == config.GROUP_NAME:
                    logger.log_trade_event('main', f"✅ Found target group: {config.GROUP_NAME}")
                    return dialog.entity
                    
            self.logger.error(f"❌ Group '{config.GROUP_NAME}' not found")
            self.logger.error("Please ensure you are a member of the group and the name is correct")
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding target group: {e}")
            return None
    
    async def setup_message_handler(self, target_group):
        """Setup the message handler for the target group"""
        @self.client.on(events.NewMessage(chats=target_group))
        async def message_handler(event):
            """Handle incoming messages from the specified group"""
            try:
                self.last_heartbeat = datetime.now()
                
                # Process the message
                result = await self.signal_handler.handle_telegram_message(event.message.text)
                
                # Log the result
                if result["status"] == "error":
                    self.logger.error(f"❌ {result['message']}")
                elif result["status"] == "success":
                    logger.log_trade_event('main', f"✅ {result['message']}")
                else:
                    # Info or warning messages
                    logger.log_trade_event('main', f"ℹ️ {result['message']}")
                    
            except Exception as e:
                self.logger.error(f"❌ Error in message handler: {e}")
    
    async def start_monitoring_tasks(self):
        """Start background monitoring tasks"""
        async def position_monitor():
            """Monitor positions for timeouts and risk management"""
            while self.running:
                try:
                    if config.ENABLE_POSITION_MONITORING:
                        # Check for position timeouts
                        timeout_results = self.signal_handler.check_position_timeouts()
                        for result in timeout_results:
                            if result.get('status') == 'success':
                                logger.log_trade_event('main', f"⏰ Auto-closed: {result.get('message', 'Unknown')}")
                    
                    # Sleep for 10 seconds before next check
                    await asyncio.sleep(10)
                    
                except Exception as e:
                    self.logger.error(f"Position monitoring error: {e}")
                    await asyncio.sleep(30)  # Wait longer on error
        
        async def heartbeat_monitor():
            """Monitor system heartbeat and log status"""
            while self.running:
                try:
                    # Log system status every 5 minutes
                    await asyncio.sleep(300)
                    
                    status = self.signal_handler.get_trading_status()
                    if status.get('status') == 'success':
                        data = status['data']
                        logger.log_trade_event('main', 
                            f"💓 System Status - Market: {'OPEN' if data['market_open'] else 'CLOSED'}, "
                            f"Positions: {data['active_positions'].get('total_positions', 0)}")
                    
                except Exception as e:
                    self.logger.error(f"Heartbeat monitoring error: {e}")
        
        # Start monitoring tasks
        if config.ENABLE_POSITION_MONITORING:
            asyncio.create_task(position_monitor())
        asyncio.create_task(heartbeat_monitor())
    
    async def run(self):
        """Main run method"""
        try:
            # Initialize the bot
            if not await self.initialize():
                return False
            
            # Find target group
            target_group = await self.find_target_group()
            if not target_group:
                return False
            
            # Setup message handler
            await self.setup_message_handler(target_group)
            
            # Start monitoring tasks
            self.running = True
            await self.start_monitoring_tasks()
            
            logger.log_trade_event('main', "🔄 Bot is now listening for trading signals...")
            
            # Run until disconnected
            await self.client.run_until_disconnected()
            
        except Exception as e:
            self.logger.error(f"❌ Bot crashed: {e}")
            return False
        finally:
            self.running = False
    
    def shutdown(self):
        """Graceful shutdown"""
        self.running = False
        logger.log_trade_event('main', "🛑 Trading bot shutting down...")
        
        # Force close all positions if enabled
        if config.ENABLE_AUTO_CLOSE:
            try:
                results = self.signal_handler.force_close_all_positions()
                for result in results:
                    if result.get('status') == 'success':
                        logger.log_trade_event('main', f"🔒 Emergency close: {result.get('message', 'Unknown')}")
            except Exception as e:
                self.logger.error(f"Error during emergency position closure: {e}")

# Configuration Management Functions
def show_current_config():
    """Display current configuration"""
    print("🔧 Current Trading System Configuration")
    print("=" * 60)

    # Trading mode
    mode_desc = config.get_mode_description()
    print(f"Trading Mode: {mode_desc}")
    print()

    # Core settings
    print("📊 Core Settings:")
    print(f"  • Default Lot Size: {config.DEFAULT_LOT_SIZE}")
    print(f"  • Signal Timeout: {config.SIGNAL_TIMEOUT_SECONDS}s")
    print(f"  • Max Positions: {config.MAX_POSITIONS}")
    print(f"  • Max Daily Loss: ₹{config.MAX_DAILY_LOSS:,.2f}")
    print()

    # System flags
    print("⚙️  System Flags:")
    print(f"  • Actual Trading: {'✅ ENABLED' if config.ENABLE_ACTUAL_TRADING else '❌ DISABLED'}")
    print(f"  • Market Data Fetching: {'✅ ENABLED' if config.ENABLE_MARKET_DATA_FETCHING else '❌ DISABLED'}")
    print(f"  • Live Prices in Simulation: {'✅ ENABLED' if config.USE_LIVE_PRICES_IN_SIMULATION else '❌ DISABLED'}")
    print(f"  • CSV Logging: {'✅ ENABLED' if config.ENABLE_CSV_LOGGING else '❌ DISABLED'}")
    print(f"  • Position Monitoring: {'✅ ENABLED' if config.ENABLE_POSITION_MONITORING else '❌ DISABLED'}")
    print(f"  • Auto Close: {'✅ ENABLED' if config.ENABLE_AUTO_CLOSE else '❌ DISABLED'}")
    print()

    # Market settings
    print("🕐 Market Settings:")
    print(f"  • Trading Hours: {config.TRADING_START_TIME} - {config.TRADING_END_TIME}")
    print(f"  • Market Currently: {'🟢 OPEN' if config.is_market_open() else '🔴 CLOSED'}")
    print()

    # File paths
    print("📁 File Paths:")
    print(f"  • Instruments: {config.INSTRUMENT_FILE_PATH}")
    print(f"  • Logs: {config.LOG_DIR}")
    print(f"  • Trade Logs: {config.TRADE_LOG_DIR}")
    print()

    # Custom lot sizes
    print("🎯 Custom Lot Sizes:")
    lot_summary = config.get_custom_lot_sizes_summary()
    print(f"  • Total Custom Configurations: {lot_summary['total_custom_configurations']}")
    if lot_summary['custom_lot_sizes']:
        print("  • Configured Option Chains:")
        for option_key, lot_size in lot_summary['custom_lot_sizes'].items():
            print(f"    - {option_key}: {lot_size} lots")
    else:
        print("  • No custom lot sizes configured (using default for all)")
    print()
    print("=" * 60)

def enable_live_trading():
    """Enable live trading mode with safety warnings"""
    print("⚠️  WARNING: ENABLING LIVE TRADING MODE")
    print("⚠️  This will place REAL orders with REAL money!")
    print()
    print("Before enabling live trading, ensure you have:")
    print("  ✓ Tested thoroughly in simulation mode")
    print("  ✓ Verified your SmartAPI credentials are correct")
    print("  ✓ Set appropriate lot sizes and risk limits")
    print("  ✓ Understood all risks involved")
    print("  ✓ Have sufficient funds in your trading account")
    print()

    response = input("Are you sure you want to enable LIVE TRADING? (type 'YES' to confirm): ")

    if response.strip().upper() == 'YES':
        try:
            with open('trading_config.py', 'r') as f:
                content = f.read()

            content = content.replace(
                'ENABLE_ACTUAL_TRADING: bool = False',
                'ENABLE_ACTUAL_TRADING: bool = True'
            )

            with open('trading_config.py', 'w') as f:
                f.write(content)

            print("✅ Live trading mode ENABLED in trading_config.py")
            print("🔴 RESTART the trading bot for changes to take effect")
            print("🔴 The system will now place REAL orders!")

        except Exception as e:
            print(f"❌ Error updating configuration: {e}")
            return False
    else:
        print("❌ Live trading mode NOT enabled")
        return False

    return True

def enable_simulation_mode():
    """Enable simulation mode (safe testing)"""
    print("✅ Enabling simulation mode...")
    print("✅ Orders will NOT be placed, but market data will be fetched")
    print("✅ All signals will be processed and logged")
    print("✅ CSV files will contain real market prices")

    try:
        with open('trading_config.py', 'r') as f:
            content = f.read()

        content = content.replace(
            'ENABLE_ACTUAL_TRADING: bool = True',
            'ENABLE_ACTUAL_TRADING: bool = False'
        )

        with open('trading_config.py', 'w') as f:
            f.write(content)

        print("✅ Simulation mode ENABLED in trading_config.py")
        print("🟡 RESTART the trading bot for changes to take effect")
        print("🟡 The system will now run in safe simulation mode")

    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return False

    return True

def update_lot_size(new_lot_size):
    """Update the default lot size"""
    try:
        new_lot_size = int(new_lot_size)
        if new_lot_size < 1:
            print("❌ Lot size must be at least 1")
            return False

        with open('trading_config.py', 'r') as f:
            content = f.read()

        pattern = r'DEFAULT_LOT_SIZE: int = \d+'
        replacement = f'DEFAULT_LOT_SIZE: int = {new_lot_size}'
        content = re.sub(pattern, replacement, content)

        with open('trading_config.py', 'w') as f:
            f.write(content)

        print(f"✅ Default lot size updated to {new_lot_size}")
        print("🔄 RESTART the trading bot for changes to take effect")

    except ValueError:
        print("❌ Invalid lot size. Please enter a number.")
        return False
    except Exception as e:
        print(f"❌ Error updating lot size: {e}")
        return False

    return True

def add_custom_lot_size(option_spec, lot_size):
    """Add a custom lot size for a specific option chain"""
    try:
        # Parse option specification: SYMBOL_STRIKE_OPTIONTYPE_EXPIRY or SYMBOL STRIKE OPTIONTYPE EXPIRY
        if '_' in option_spec:
            parts = option_spec.split('_')
        else:
            parts = option_spec.split()

        if len(parts) < 3:
            print("❌ Invalid option specification. Use format: SYMBOL_STRIKE_OPTIONTYPE_EXPIRY")
            print("   Example: NIFTY_25000_CE_16SEP or 'NIFTY 25000 CE 16SEP'")
            return False

        symbol = parts[0].upper()
        strike = float(parts[1])
        option_type = parts[2].upper()
        expiry = parts[3] if len(parts) > 3 else ""

        lot_size = int(lot_size)
        if lot_size < 1:
            print("❌ Lot size must be at least 1")
            return False

        # Add the custom lot size
        success = config.add_custom_lot_size(symbol, strike, option_type, expiry, lot_size)
        if success:
            # Update the configuration file
            option_key = config.create_option_chain_key(symbol, strike, option_type, expiry)
            update_custom_lot_sizes_in_file(option_key, lot_size, action='add')
            print(f"✅ Custom lot size added: {option_key} = {lot_size} lots")
            print("🔄 RESTART the trading bot for changes to take effect")
        else:
            print("❌ Failed to add custom lot size")
            return False

    except ValueError:
        print("❌ Invalid strike price or lot size. Please enter valid numbers.")
        return False
    except Exception as e:
        print(f"❌ Error adding custom lot size: {e}")
        return False

    return True

def remove_custom_lot_size(option_spec):
    """Remove a custom lot size for a specific option chain"""
    try:
        # Parse option specification
        if '_' in option_spec:
            parts = option_spec.split('_')
        else:
            parts = option_spec.split()

        if len(parts) < 3:
            print("❌ Invalid option specification. Use format: SYMBOL_STRIKE_OPTIONTYPE_EXPIRY")
            return False

        symbol = parts[0].upper()
        strike = float(parts[1])
        option_type = parts[2].upper()
        expiry = parts[3] if len(parts) > 3 else ""

        option_key = config.create_option_chain_key(symbol, strike, option_type, expiry)

        # Check if the entry exists in the file first
        with open('trading_config.py', 'r') as f:
            content = f.read()

        if f'CUSTOM_LOT_SIZES["{option_key}"]' in content:
            # Update the configuration file first
            update_custom_lot_sizes_in_file(option_key, 0, action='remove')

            # Also remove from in-memory config if it exists
            config.remove_custom_lot_size(symbol, strike, option_type, expiry)

            print(f"✅ Custom lot size removed for: {option_key}")
            print("🔄 RESTART the trading bot for changes to take effect")
        else:
            print(f"❌ No custom lot size found for: {option_spec}")
            return False

    except ValueError:
        print("❌ Invalid strike price. Please enter a valid number.")
        return False
    except Exception as e:
        print(f"❌ Error removing custom lot size: {e}")
        return False

    return True

def update_custom_lot_sizes_in_file(option_key, lot_size, action='add'):
    """Update the custom lot sizes in the configuration file"""
    try:
        with open('trading_config.py', 'r') as f:
            content = f.read()

        if action == 'add':
            # Check if this option key already exists
            existing_pattern = rf'self\.CUSTOM_LOT_SIZES\["{option_key}"\]\s*=\s*\d+'
            if re.search(existing_pattern, content):
                # Update existing entry
                replacement = f'self.CUSTOM_LOT_SIZES["{option_key}"] = {lot_size}'
                content = re.sub(existing_pattern, replacement, content)
            else:
                # Add new entry before the 'pass' statement
                # First try to add after existing custom lot size entries
                if 'self.CUSTOM_LOT_SIZES[' in content:
                    # Find the last custom lot size entry and add after it
                    pattern = r'(self\.CUSTOM_LOT_SIZES\[[^\]]+\]\s*=\s*\d+\s*\n)(\s*pass)'
                    replacement = rf'\1            self.CUSTOM_LOT_SIZES["{option_key}"] = {lot_size}\n\2'
                    content = re.sub(pattern, replacement, content)
                else:
                    # No existing entries, add after the comment
                    pattern = r'(# Custom lot sizes will be added here by the configuration manager\s*\n)(\s*pass)'
                    replacement = rf'\1            self.CUSTOM_LOT_SIZES["{option_key}"] = {lot_size}\n\2'
                    content = re.sub(pattern, replacement, content)

        elif action == 'remove':
            # Remove the entry
            pattern = rf'^\s*self\.CUSTOM_LOT_SIZES\["{re.escape(option_key)}"\]\s*=\s*\d+\s*\n'
            content = re.sub(pattern, '', content, flags=re.MULTILINE)

        with open('trading_config.py', 'w') as f:
            f.write(content)

    except Exception as e:
        print(f"⚠️  Warning: Could not update configuration file: {e}")
        print("   The change is active for this session but won't persist after restart")

def list_custom_lot_sizes():
    """List all configured custom lot sizes"""
    print("🎯 Custom Lot Size Configuration")
    print("=" * 50)

    lot_summary = config.get_custom_lot_sizes_summary()
    print(f"Default Lot Size: {lot_summary['default_lot_size']}")
    print(f"Custom Configurations: {lot_summary['total_custom_configurations']}")
    print()

    if lot_summary['custom_lot_sizes']:
        print("Configured Option Chains:")
        for option_key, lot_size in sorted(lot_summary['custom_lot_sizes'].items()):
            print(f"  {option_key}: {lot_size} lots")
    else:
        print("No custom lot sizes configured.")
        print("All option trades will use the default lot size.")

    print()
    print("To add custom lot size:")
    print("  uv run python main.py --add-lot-size NIFTY_25000_CE_16SEP --size 2")
    print("To remove custom lot size:")
    print("  uv run python main.py --remove-lot-size NIFTY_25000_CE_16SEP")
    print("=" * 50)

def signal_handler_func(signum, frame):
    """Handle system signals for graceful shutdown"""
    print("\n🛑 Received shutdown signal...")
    sys.exit(0)

async def run_trading_bot():
    """Run the trading bot"""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler_func)
    signal.signal(signal.SIGTERM, signal_handler_func)

    # Create and run the bot
    bot = TradingBot()

    try:
        await bot.run()
    except KeyboardInterrupt:
        logger.log_trade_event('main', "🛑 Bot stopped by user")
        bot.shutdown()
    except Exception as e:
        logger.log_error('main', f"❌ Unexpected error: {e}")
        bot.shutdown()
        sys.exit(1)

def main():
    """Main entry point with configuration management"""
    parser = argparse.ArgumentParser(
        description='Indian Market Trading Bot - Automated trading with Telegram signals',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                          # Start trading bot
  python main.py --config show                           # Show current configuration
  python main.py --config live                           # Enable live trading mode
  python main.py --config sim                            # Enable simulation mode
  python main.py --lot-size 2                            # Update default lot size to 2
  python main.py --list-lot-sizes                        # List all custom lot sizes
  python main.py --add-lot-size NIFTY_25000_CE_16SEP --size 2    # Add custom lot size
  python main.py --remove-lot-size NIFTY_25000_CE_16SEP          # Remove custom lot size
        """
    )

    parser.add_argument('--config', choices=['show', 'live', 'sim', 'simulation'],
                       help='Configuration management commands')
    parser.add_argument('--lot-size', type=int, help='Update default lot size')
    parser.add_argument('--list-lot-sizes', action='store_true',
                       help='List all configured custom lot sizes')
    parser.add_argument('--add-lot-size', type=str,
                       help='Add custom lot size for option chain (format: SYMBOL_STRIKE_OPTIONTYPE_EXPIRY)')
    parser.add_argument('--remove-lot-size', type=str,
                       help='Remove custom lot size for option chain')
    parser.add_argument('--size', type=int,
                       help='Lot size value (used with --add-lot-size)')

    args = parser.parse_args()

    try:
        # Handle configuration commands
        if args.config:
            if args.config == 'show':
                show_current_config()
            elif args.config == 'live':
                enable_live_trading()
            elif args.config in ['sim', 'simulation']:
                enable_simulation_mode()
            return

        # Handle lot size update
        if args.lot_size:
            update_lot_size(args.lot_size)
            return

        # Handle custom lot size commands
        if args.list_lot_sizes:
            list_custom_lot_sizes()
            return

        if args.add_lot_size:
            if args.size:
                add_custom_lot_size(args.add_lot_size, args.size)
            else:
                print("❌ Please specify lot size with --size parameter")
                print("Example: python main.py --add-lot-size NIFTY_25000_CE_16SEP --size 2")
            return

        if args.remove_lot_size:
            remove_custom_lot_size(args.remove_lot_size)
            return

        # No arguments provided - start the trading bot
        print("🚀 Starting Indian Market Trading Bot...")
        print(f"Mode: {'🔴 LIVE TRADING' if config.ENABLE_ACTUAL_TRADING else '🟡 SIMULATION'}")
        print("Press Ctrl+C to stop")
        print()

        asyncio.run(run_trading_bot())

    except KeyboardInterrupt:
        print("\n🛑 Trading bot stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
