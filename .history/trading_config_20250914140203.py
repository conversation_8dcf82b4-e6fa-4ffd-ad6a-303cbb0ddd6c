"""
Enhanced Trading System Configuration
Centralized configuration for all trading parameters
"""

import os
from dataclasses import dataclass
from typing import Optional
from datetime import datetime, time

@dataclass
class TradingConfig:
    """Comprehensive trading system configuration"""
    
    # Telegram Configuration
    API_ID: str = "25551963"
    API_HASH: str = "9f4e9e1abbddf507689ad1eebb6b9ee0"
    SESSION_NAME: str = "telegram_bot_session"
    GROUP_NAME: str = "ಆತ್ಮ ನಿರ್ಭರ"
    
    # SmartAPI Configuration
    SMARTAPI_API_KEY: str = "GvbeXvJX"
    SMARTAPI_USER_ID: str = "s60593162"
    SMARTAPI_PIN: str = "2010"
    SMARTAPI_TOTP_SECRET: str = "EEAZPGYT7COLYB32WAOEO7MLMY"
    SMARTAPI_RATE_LIMIT_DELAY: int = 3
    SMARTAPI_MAX_RECONNECT_ATTEMPTS: int = 3
    SMARTAPI_AUTH_DELAY: int = 15
    
    # Trading Parameters
    TRADING_START_TIME: str = "09:15"
    TRADING_END_TIME: str = "15:30"
    DEFAULT_LOT_SIZE: int = 1  # Number of lots per trade
    SIGNAL_TIMEOUT_SECONDS: int = 30  # Auto-close if no signal received

    # Custom lot sizes for specific option chains
    # Format: "SYMBOL_STRIKE_OPTIONTYPE_EXPIRY": lot_size
    # Example: "NIFTY_25000_CE_16SEP": 2, "BANKNIFTY_45000_PE_18SEP": 3
    CUSTOM_LOT_SIZES: dict = None  # Will be initialized in __post_init__
    
    # Risk Management
    MAX_POSITIONS: int = 5  # Maximum concurrent positions
    MAX_DAILY_LOSS: float = 10000.0  # Maximum daily loss limit
    ENABLE_STOP_LOSS: bool = True
    ENABLE_AUTO_CLOSE: bool = True
    
    # Trading Mode Configuration
    ENABLE_ACTUAL_TRADING: bool = False  # Set to True for live trading
    ENABLE_MARKET_DATA_FETCHING: bool = True  # Always fetch real market prices
    ENABLE_CSV_LOGGING: bool = True
    ENABLE_POSITION_MONITORING: bool = True

    # Market Data Configuration
    DISABLE_WEBSOCKET: bool = False
    ENABLE_LTP_FETCHING: bool = True
    ENABLE_DEPTH_DATA: bool = True
    USE_LIVE_PRICES_IN_SIMULATION: bool = True  # Use real prices even in simulation mode

    # File Paths
    INSTRUMENT_FILE_PATH: str = "Dependencies"
    LOG_DIR: str = "logs"
    TRADE_LOG_DIR: str = "trade_logs"
    
    # Signal Format Mapping
    SIGNAL_FORMATS = {
        "FORMAT1": "BUY",      # ===Algo_Trading===$TRADE$BUY$...
        "FORMAT2": "HOLD",     # ===Algo_Trading===$INTIMATION$Continue to hold...
        "FORMAT3": "CLOSE",    # ===Algo_Trading===$TRADE$CLOSE$...$reason
        "FORMAT4": "UPDATE",   # ===Algo_Trading===$Update$STOP LOSS to$...
        "FORMAT5": "CLOSE"     # ===Algo_Trading===$TRADE$CLOSE$...$crossover_exit
    }

    def __post_init__(self):
        """Initialize custom lot sizes if not already set"""
        if self.CUSTOM_LOT_SIZES is None:
            # Initialize with any custom lot sizes defined below
            self.CUSTOM_LOT_SIZES = {}

            # Add your custom lot sizes here:
            # Format: "SYMBOL_STRIKE_OPTIONTYPE_EXPIRY": lot_size
            # Examples:
            # self.CUSTOM_LOT_SIZES["NIFTY_25000_CE_16SEP"] = 2
            # self.CUSTOM_LOT_SIZES["BANKNIFTY_45000_PE_18SEP"] = 3
            # self.CUSTOM_LOT_SIZES["SENSEX_81500_PUT_18SEP"] = 1

            # Custom lot sizes will be added here by the configuration manager
            self.CUSTOM_LOT_SIZES["NIFTY_24000_CE_20SEP"] = 4
            pass
    
    def validate(self) -> bool:
        """Validate required configuration values"""
        required_fields = [
            'API_ID', 'API_HASH', 'GROUP_NAME',
            'SMARTAPI_API_KEY', 'SMARTAPI_USER_ID',
            'SMARTAPI_PIN', 'SMARTAPI_TOTP_SECRET'
        ]
        
        for field in required_fields:
            if not getattr(self, field):
                return False
        return True

    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        now = datetime.now().time()
        start = datetime.strptime(self.TRADING_START_TIME, "%H:%M").time()
        end = datetime.strptime(self.TRADING_END_TIME, "%H:%M").time()
        
        # Check if it's a weekday
        if datetime.now().weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
            
        return start <= now <= end

    def get_trading_status(self) -> dict:
        """Get current trading system status"""
        return {
            "market_open": self.is_market_open(),
            "trading_enabled": self.ENABLE_ACTUAL_TRADING,
            "market_data_enabled": self.ENABLE_MARKET_DATA_FETCHING,
            "simulation_mode": not self.ENABLE_ACTUAL_TRADING,
            "live_prices_in_simulation": self.USE_LIVE_PRICES_IN_SIMULATION,
            "default_lot_size": self.DEFAULT_LOT_SIZE,
            "signal_timeout": self.SIGNAL_TIMEOUT_SECONDS,
            "max_positions": self.MAX_POSITIONS,
            "auto_close_enabled": self.ENABLE_AUTO_CLOSE,
            "csv_logging": self.ENABLE_CSV_LOGGING
        }

    def enable_live_trading(self):
        """Enable live trading mode with all safety checks"""
        print("⚠️  ENABLING LIVE TRADING MODE")
        print("⚠️  This will place REAL orders with REAL money!")
        print("⚠️  Make sure you have:")
        print("   - Tested thoroughly in simulation mode")
        print("   - Verified your SmartAPI credentials")
        print("   - Set appropriate lot sizes and risk limits")
        print("   - Understood all risks involved")

        self.ENABLE_ACTUAL_TRADING = True
        self.ENABLE_MARKET_DATA_FETCHING = True
        self.USE_LIVE_PRICES_IN_SIMULATION = True

        print("✅ Live trading mode ENABLED")
        return True

    def enable_simulation_mode(self):
        """Enable simulation mode (safe testing)"""
        print("✅ ENABLING SIMULATION MODE")
        print("✅ Orders will NOT be placed, but market data will be fetched")
        print("✅ All signals will be processed and logged")
        print("✅ CSV files will contain real market prices")

        self.ENABLE_ACTUAL_TRADING = False
        self.ENABLE_MARKET_DATA_FETCHING = True
        self.USE_LIVE_PRICES_IN_SIMULATION = True

        print("✅ Simulation mode ENABLED")
        return True

    def get_mode_description(self) -> str:
        """Get a description of the current trading mode"""
        if self.ENABLE_ACTUAL_TRADING:
            return "🔴 LIVE TRADING MODE - Real orders will be placed!"
        elif self.ENABLE_MARKET_DATA_FETCHING:
            return "🟡 SIMULATION MODE - Real prices, no orders"
        else:
            return "🟢 OFFLINE MODE - No market data, no orders"

    def create_option_chain_key(self, symbol: str, strike: float, option_type: str, expiry_date: str) -> str:
        """
        Create a standardized option chain key for lot size lookup
        Format: SYMBOL_STRIKE_OPTIONTYPE_EXPIRY
        Example: NIFTY_25000_CE_16SEP
        """
        try:
            # Normalize inputs
            symbol = symbol.upper().strip()
            option_type = option_type.upper().strip()

            # Convert strike to int if it's a whole number, otherwise keep as float
            if float(strike) == int(strike):
                strike_str = str(int(strike))
            else:
                strike_str = str(strike)

            # Normalize expiry date - remove spaces and convert to format like "16SEP"
            expiry_parts = expiry_date.strip().split()
            if len(expiry_parts) >= 2:
                expiry_normalized = f"{expiry_parts[0]}{expiry_parts[1].upper()}"
            else:
                expiry_normalized = expiry_date.replace(" ", "").upper()

            # Create the key
            option_key = f"{symbol}_{strike_str}_{option_type}_{expiry_normalized}"
            return option_key

        except Exception as e:
            # If there's any error in key creation, return a fallback key
            return f"{symbol}_{strike}_{option_type}_{expiry_date}".replace(" ", "_").upper()

    def get_lot_size_for_option(self, symbol: str, strike: float = None, option_type: str = None, expiry_date: str = None) -> int:
        """
        Get the appropriate lot size for a given option chain
        Returns custom lot size if configured, otherwise returns DEFAULT_LOT_SIZE
        """
        try:
            # If it's not an option trade (missing strike/option_type), use default
            if strike is None or option_type is None:
                return self.DEFAULT_LOT_SIZE

            # Create the option chain key
            option_key = self.create_option_chain_key(symbol, strike, option_type, expiry_date or "")

            # Check if custom lot size is configured for this option chain
            if option_key in self.CUSTOM_LOT_SIZES:
                custom_lot_size = self.CUSTOM_LOT_SIZES[option_key]
                return max(1, int(custom_lot_size))  # Ensure minimum lot size of 1

            # Also try without expiry date for more flexible matching
            if expiry_date:
                option_key_no_expiry = f"{symbol}_{int(strike) if float(strike) == int(strike) else strike}_{option_type}"
                if option_key_no_expiry in self.CUSTOM_LOT_SIZES:
                    custom_lot_size = self.CUSTOM_LOT_SIZES[option_key_no_expiry]
                    return max(1, int(custom_lot_size))

            # No custom lot size found, return default
            return self.DEFAULT_LOT_SIZE

        except Exception as e:
            # On any error, return default lot size
            return self.DEFAULT_LOT_SIZE

    def add_custom_lot_size(self, symbol: str, strike: float, option_type: str, expiry_date: str, lot_size: int) -> bool:
        """
        Add or update a custom lot size for a specific option chain
        Returns True if successful, False otherwise
        """
        try:
            if lot_size < 1:
                return False

            option_key = self.create_option_chain_key(symbol, strike, option_type, expiry_date)
            self.CUSTOM_LOT_SIZES[option_key] = lot_size
            return True

        except Exception:
            return False

    def remove_custom_lot_size(self, symbol: str, strike: float, option_type: str, expiry_date: str) -> bool:
        """
        Remove a custom lot size for a specific option chain
        Returns True if successful, False otherwise
        """
        try:
            option_key = self.create_option_chain_key(symbol, strike, option_type, expiry_date)
            if option_key in self.CUSTOM_LOT_SIZES:
                del self.CUSTOM_LOT_SIZES[option_key]
                return True
            return False

        except Exception:
            return False

    def get_custom_lot_sizes_summary(self) -> dict:
        """Get a summary of all configured custom lot sizes"""
        return {
            "total_custom_configurations": len(self.CUSTOM_LOT_SIZES),
            "default_lot_size": self.DEFAULT_LOT_SIZE,
            "custom_lot_sizes": dict(self.CUSTOM_LOT_SIZES)
        }

# Global configuration instance
config = TradingConfig()
config.__post_init__()  # Initialize custom lot sizes

# Environment variable overrides (for production deployment)
def load_from_env():
    """Load configuration from environment variables if available"""
    config.API_ID = os.getenv('TELEGRAM_API_ID', config.API_ID)
    config.API_HASH = os.getenv('TELEGRAM_API_HASH', config.API_HASH)
    config.GROUP_NAME = os.getenv('TELEGRAM_GROUP_NAME', config.GROUP_NAME)
    
    config.SMARTAPI_API_KEY = os.getenv('SMARTAPI_API_KEY', config.SMARTAPI_API_KEY)
    config.SMARTAPI_USER_ID = os.getenv('SMARTAPI_USER_ID', config.SMARTAPI_USER_ID)
    config.SMARTAPI_PIN = os.getenv('SMARTAPI_PIN', config.SMARTAPI_PIN)
    config.SMARTAPI_TOTP_SECRET = os.getenv('SMARTAPI_TOTP_SECRET', config.SMARTAPI_TOTP_SECRET)
    
    config.DEFAULT_LOT_SIZE = int(os.getenv('DEFAULT_LOT_SIZE', config.DEFAULT_LOT_SIZE))
    config.SIGNAL_TIMEOUT_SECONDS = int(os.getenv('SIGNAL_TIMEOUT_SECONDS', config.SIGNAL_TIMEOUT_SECONDS))
    config.ENABLE_ACTUAL_TRADING = os.getenv('ENABLE_ACTUAL_TRADING', 'false').lower() == 'true'
    
    return config

# Load environment variables on import
load_from_env()
