#!/usr/bin/env python3
"""
Test SmartAPI connection and market data fetching
"""

import asyncio
from utils.api_handler import APIHandler, TradeSignal
from utils.logger import logger
from trading_config import config

def test_smartapi_connection():
    """Test SmartAPI connection and basic functionality"""
    print("🔄 Testing SmartAPI Connection...")
    print("=" * 50)
    
    # Initialize API handler
    api = APIHandler()
    
    # Test connection
    connection_status = api.test_smartapi_connection()
    print(f"Connection Status: {connection_status['status']}")
    print(f"Message: {connection_status['message']}")
    print(f"Connected: {connection_status['connected']}")
    
    if connection_status.get('data'):
        data = connection_status['data']
        print(f"Available Cash: ₹{data.get('availablecash', 'N/A')}")
        print(f"Used Margin: ₹{data.get('utilisedmargin', 'N/A')}")
    
    print()
    
    # Test market data fetching with a common instrument
    if connection_status['connected']:
        print("🔄 Testing Market Data Fetching...")
        print("-" * 30)
        
        # Create a test signal for NIFTY
        test_signal = TradeSignal(
            signal_type="BUY",
            action="BUY",
            symbol="NIFTY",
            strike=None,
            option_type=None,
            expiry_date=None,
            quantity=1,
            price=None,
            stop_loss=None,
            target=None,
            timestamp=None,
            raw_message="Test signal for NIFTY"
        )
        
        # Find instrument
        instrument = api.find_instrument(test_signal)
        if instrument:
            print(f"✅ Found instrument: {instrument['tradingsymbol']}")
            print(f"Exchange: {instrument['exchange']}")
            print(f"Token: {instrument['token']}")
            
            # Get market data
            market_data = api.get_market_data(instrument)
            if market_data:
                print(f"✅ Market data fetched successfully!")
                print(f"LTP: ₹{market_data.get('ltp', 'N/A')}")
                print(f"Open: ₹{market_data.get('open', 'N/A')}")
                print(f"High: ₹{market_data.get('high', 'N/A')}")
                print(f"Low: ₹{market_data.get('low', 'N/A')}")
                print(f"Close: ₹{market_data.get('close', 'N/A')}")
            else:
                print("❌ Failed to fetch market data")
        else:
            print("❌ Could not find NIFTY instrument")
            
        print()
        
        # Test with an option instrument
        print("🔄 Testing Option Market Data...")
        print("-" * 30)
        
        option_signal = TradeSignal(
            signal_type="BUY",
            action="BUY", 
            symbol="NIFTY",
            strike=25000.0,
            option_type="CE",
            expiry_date="19 SEP",
            quantity=1,
            price=None,
            stop_loss=None,
            target=None,
            timestamp=None,
            raw_message="Test signal for NIFTY option"
        )
        
        option_instrument = api.find_instrument(option_signal)
        if option_instrument:
            print(f"✅ Found option instrument: {option_instrument['tradingsymbol']}")
            print(f"Exchange: {option_instrument['exchange']}")
            print(f"Token: {option_instrument['token']}")
            print(f"Lot Size: {option_instrument['lot_size']}")
            
            # Get option market data
            option_market_data = api.get_market_data(option_instrument)
            if option_market_data:
                print(f"✅ Option market data fetched successfully!")
                print(f"LTP: ₹{option_market_data.get('ltp', 'N/A')}")
                print(f"Open: ₹{option_market_data.get('open', 'N/A')}")
                print(f"High: ₹{option_market_data.get('high', 'N/A')}")
                print(f"Low: ₹{option_market_data.get('low', 'N/A')}")
            else:
                print("❌ Failed to fetch option market data")
        else:
            print("❌ Could not find NIFTY option instrument")
    
    print()
    print("=" * 50)
    print("✅ SmartAPI Test Complete!")

if __name__ == "__main__":
    test_smartapi_connection()
